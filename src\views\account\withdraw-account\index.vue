<!-- 提现账户列表 -->
<script setup lang="ts">
import { ref, onMounted, computed } from "vue";
import { Status } from "@/components/ZAccountCard/types";
import { PN_VERIFY_TYPE } from "@/components/ZVerifyDialog/types";
import VerifyDialogPreconditions from "@/components/ZVerifyDialog/VerifyDialogPreconditions.vue";
import VerifyDialogLoginPassword from "@/components/ZVerifyDialog/VerifyDialogLoginPassword.vue";
import { showToast } from "vant";
import router from "@/router";
import { useWithdrawStore } from "@/stores/withdraw";
import { setLocalStorage } from "@/utils/core/Storage";
import { getWithdrawAccounts } from "@/api/withdrawal";
import { useVerifyPreconditions } from "@/composables/useVerifyPreconditions";
import { storeToRefs } from "pinia";
import { useGameStore } from "@/stores/game";
import { verifyLoginPassword } from "@/utils/HttpProxy";
import { Md5 } from "@/utils/core/Md5";

// 列表
const accounts = ref<any[]>([]);
// 总限制数
const totalNum = ref(0);
// 当前数
const currentNum = ref(0);
// 登录密码
const showVerifyLoginPasswordDialog = ref(false);

const withdrawStore = useWithdrawStore();

const { withdrawData } = storeToRefs(withdrawStore);

// 使用验证前置条件的 composable
const { showVerifyDialogPreconditions, verifyPreconditions, handlePreconditionsConfirm } =
  useVerifyPreconditions();

const editWithdrawAccountType = ref(PN_VERIFY_TYPE.AddWithdrawAccount);

// 计算属性：是否可以添加账户
const canAddAccount = computed(() => accounts.value.length < totalNum.value);

const handleVaild = () => {
  // 添加、修改提现账号是否需要验证登录密码。
  // 1 需要，0 不需要。
  if (editWithdrawAccountType.value === PN_VERIFY_TYPE.AddWithdrawAccount) {
    verifyPreconditions(() => {
      handleNext();
    });
  } else if (editWithdrawAccountType.value === PN_VERIFY_TYPE.ChangeWithdrawAccount) {
    verifyPreconditions(() => {
      handleNext();
    });
  }
};

// 添加账户
const handleAddAccount = (e) => {
  // 阻止冒泡
  e.stopPropagation();
  if (withdrawData.value.length === 0) {
    showToast("Unable to add account at this time, please contact customer service");
    return;
  }
  editWithdrawAccountType.value = PN_VERIFY_TYPE.AddWithdrawAccount;
  //最多可以添加10个提现账号
  if (accounts && accounts.value?.length >= totalNum.value) {
    showToast(`Up to ${totalNum.value} Withdrawal Accounts can be added.`);
    return;
  }
  handleVaild();
};
// 编辑弹窗
const handleEditAccount = (item: any) => {
  editWithdrawAccountType.value = PN_VERIFY_TYPE.ChangeWithdrawAccount;
  setLocalStorage("withdrawAccountSelected", item);
  handleVaild();
};

const handleVerifyCb = (loginPassword) => {
  verifyLoginPassword({
    params: {
      withdraw_password: Md5.hashStr(loginPassword).toString(),
    },
    successCallBack: () => {
      handleNext();
    },
    failCallBack: () => {},
  });
};

const handleNext = () => {
  if (editWithdrawAccountType.value === PN_VERIFY_TYPE.AddWithdrawAccount) {
    router.push(`/account/withdraw-account/edit`);
  } else {
    router.push(`/account/withdraw-account/edit?type=edit`);
  }
};

const getAccounts = async () => {
  const response = await getWithdrawAccounts();
  const { list, current_num, total_num } = response.data || response;
  accounts.value = list;
  currentNum.value = current_num;
  totalNum.value = total_num;
  return {
    list,
    current_num,
    total_num,
  };
};

onMounted(() => {
  withdrawStore.getWithdrawConfigList();
});
</script>

<template>
  <ZPage :request="getAccounts">
    <template v-if="accounts.length === 0">
      <ZNoData></ZNoData>
    </template>
    <div class="content" v-else :class="{ 'has-add-btn': canAddAccount }">
      <div class="card-wrap">
        <template v-for="account in accounts" :key="account.id">
          <ZWithdrawAccountCard
            :item="account"
            :status="Status.EDIT"
            :cardHeight="124"
            class="card-item"
            @click="handleEditAccount"
          >
          </ZWithdrawAccountCard>
        </template>
      </div>
    </div>
    <div class="add_btn" v-if="canAddAccount">
      <ZButton @click="handleAddAccount"> Add Withdrawal Account </ZButton>
    </div>
    <!-- 绑定手机号、登录密码 -->
    <VerifyDialogPreconditions
      v-model:showDialog="showVerifyDialogPreconditions"
      :succCallBack="handlePreconditionsConfirm"
    >
    </VerifyDialogPreconditions>
    <!-- 登录密码验证 -->
    <VerifyDialogLoginPassword
      v-model:showDialog="showVerifyLoginPasswordDialog"
      :succCallBack="handleVerifyCb"
    ></VerifyDialogLoginPassword>
  </ZPage>
</template>

<style scoped lang="scss">
.content {
  display: flex;
  flex-direction: column;
  padding: 0 16px;
  padding-top: 16px;
  // 确保内容区域可以滚动，高度计算要考虑导航栏和底部按钮
  height: calc(100% - env(safe-area-inset-top, 0px));
  overflow-y: auto;
  overflow-x: hidden;
  -webkit-overflow-scrolling: touch;
  box-sizing: border-box;

  &.has-add-btn {
    // 有添加按钮时，减去按钮高度
    height: calc(100% - 80px - env(safe-area-inset-top, 0px) - env(safe-area-inset-bottom, 0px));
  }

  .card-wrap {
    display: flex;
    flex-direction: column;

    .card-item {
      margin-bottom: 16px;

      &:first-child {
        margin-top: 0;
      }
    }
  }
}

.add_btn {
  position: fixed;
  bottom: 0;
  left: 16px;
  right: 16px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  // 添加上边框和阴影，增强视觉层次
  // border-top: 1px solid rgba(0, 0, 0, 0.05);
  // box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
  // 确保在最顶层
  z-index: 100;
  // 适配安全区域
  padding-bottom: env(safe-area-inset-bottom, 0px);
  box-sizing: border-box;
}
</style>
