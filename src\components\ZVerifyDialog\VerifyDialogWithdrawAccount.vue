<!-- 添加、编辑账户弹窗 -->
<template>
  <ZActionSheet
    v-model="visible"
    :title="getTypeInfo?.title"
    confirmText="Confirm"
    :showCancelButton="false"
    :onConfirm="handleConfirm"
    :onCancel="handleCancel"
  >
    <div class="dialog-content">
      <div class="send-code-tip">
        A text message with a 6-digit code was just sent to
        <b>{{ formattedUserPhone }}</b>
      </div>
      <div class="phone-input-container">
        <label class="verCode">Enter a verification code</label>
        <div class="phone-input-code">
          <VerificationCodeInput
            class="verificationCode"
            v-model="verificationCode"
            ref="verificationCodeRef"
          />
          <CodeCountdownButton ref="countdownButtonRef" @click="checkPhoneIsRegister" />
        </div>
      </div>
    </div>
  </ZActionSheet>
</template>

<script setup lang="ts">
import { sendCodeMsg, verifyCode } from "@/api/setPhoneNumber";
import { GEETEST_TYPE } from "@/utils/GeetestMgr";
import { executeVerification, type VerificationResult } from "@/utils/VerificationMgr";
import { useGlobalStore } from "@/stores/global";
import { ref, computed } from "vue";
import { showToast } from "vant";
import { GlobalEnum } from "@/utils/config/GlobalEnum";
import enTranslations from "@/utils/I18n/en.json";
import { PN_VERIFY_TYPE } from "./types";
import CodeCountdownButton from "@/components/CodeCountdownButton.vue";
import VerificationCodeInput from "@/components/VerificationCodeInput/index.vue";
import { handleSmsResponse } from "@/utils/responseProcessing";
import { formatPhoneNumber } from "@/utils/core/tools";

const globalStore = useGlobalStore();
const { userInfo } = storeToRefs(globalStore);
const countdownButtonRef = ref();

const props = defineProps({
  // 显示弹窗
  showDialog: {
    type: Boolean,
    default: false,
    required: true,
  },
  //  类型:添加：3 、编辑 4
  verifyType: {
    type: Number,
    default: 3,
    required: true,
  },
  // 成功回调
  succCallBack: {
    type: Function,
    default: () => {},
    required: false,
  },
  changePhone: {
    type: String,
    default: "",
    required: false,
  },
});

// 弹窗是否显示
const visible = ref(props.showDialog);
// 验证码
const verificationCode = ref("");
// 是否已发送验证码
const hasSentCode = ref(false);
const verificationCodeRef = ref();

const resetData = () => {
  verificationCode.value = ""; // 重置验证码
  hasSentCode.value = false;
  verificationCodeRef.value?.clear();
};

watch(
  () => props.showDialog,
  (val) => {
    visible.value = val;
    if (!val) {
      resetData();
    }
  }
);

// 监听 visible 变化，同步到父组件
watch(
  () => visible.value,
  (val) => {
    if (val !== props.showDialog) {
      emit("update:showDialog", val);
    }
  }
);

const getTypeInfo = computed(() => {
  if (props.verifyType === PN_VERIFY_TYPE.AddWithdrawAccount) {
    return {
      title: "Add Fund Account",
      gtype: GEETEST_TYPE.bind_withdraw_account_code,
      msgType: GlobalEnum.SMS_TYPE.BIND_WITHDRAW_ACCOUNT,
    };
  } else if (props.verifyType === PN_VERIFY_TYPE.ChangeWithdrawAccount) {
    return {
      title: "Change Fund Account",
      gtype: GEETEST_TYPE.change_withdraw_account_code,
      msgType: GlobalEnum.SMS_TYPE.UPDATE_WITHDRAW_ACCOUNT,
    };
  }
});

const formattedUserPhone = computed(() => {
  return formatPhoneNumber(props.changePhone as string);
});

//发送验证码之前先验证
const checkPhoneIsRegister = () => {
  executeVerification(
    GEETEST_TYPE.bind_withdraw_account_code,
    (result: VerificationResult | false) => {
      if (result && result.success) {
        const ret = {
          // Geetest 参数
          geetest_guard: result.data?.geetest_guard || "",
          userInfo: result.data?.userInfo || "",
          geetest_captcha: result.data?.geetest_captcha || "",
          buds: result.data?.buds || "64",
          // Cloudflare 参数
          "cf-token": result.data?.["cf-token"] || "",
          "cf-scene": result.data?.["cf-scene"] || "",
        };
        checkPhoneIsRegister_true(ret);
      }
    }
  );
};
//发送验证码
const checkPhoneIsRegister_true = async (ret) => {
  let params = {
    phone: userInfo.value.phone,
    telephoneCode: "+63",
    type: getTypeInfo.value?.msgType || "",
    // Geetest 参数
    geetest_guard: ret?.geetest_guard || "",
    userInfo: ret?.userInfo || "",
    geetest_captcha: ret?.geetest_captcha || "",
    buds: ret?.buds || "64",
    // Cloudflare 参数
    "cf-token": ret?.["cf-token"] || "",
    "cf-scene": ret?.["cf-scene"] || "",
  };
  const response = await sendCodeMsg(params);

  handleSmsResponse(response, {
    countdownRef: countdownButtonRef,
    onSuccess: () => {
      hasSentCode.value = true;
    },
    onError: (code: any, message: any) => {
      console.error("发送验证码失败:", code, message);
    },
  });
};

const emit = defineEmits(["update:showDialog"]);

const handleCancel = () => {
  emit("update:showDialog", false);
};

const handleConfirm = async () => {
  if (verificationCodeRef.value?.isValid()) {
    handleCheckCode();
  } else {
    verificationCodeRef.value?.validate();
  }
};

const handleCheckCode = async () => {
  const { code, msg } = await verifyCode({
    phone: userInfo.value.phone,
    telephoneCode: "+63",
    code: verificationCode.value,
    type: getTypeInfo.value?.msgType,
  });

  if (code === 200 || code === 0) {
    handleCancel();
    props.succCallBack && props.succCallBack();
    return;
  } else {
    showToast(msg || "Verification Error,Please Try Again");
  }
};
</script>

<style scoped lang="scss">
.dialog-content {
  padding-top: 12px;
  padding-bottom: 10px;

  // 验证码步骤样式
  .send-code-tip {
    color: #222;
    font-family: Inter;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px;
    margin-bottom: 16px;

    /* 171.429% */
  }
  .phone-input-container {
    .verCode {
      color: #666;
      font-family: Inter;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
    }

    .phone-input-code {
      margin-top: 12px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 12px;
      .verificationCode {
        flex: 1;
        height: 40px;
        border: 1px solid #eee;
        border-radius: 20px;
        padding: 0 12px;
        outline: none;
        background-color: #f4f7fd;
      }
    }
  }
}

.phone-input {
  label {
    color: #666;
    font-family: Inter;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    margin-bottom: 6px;
    display: inline-block;
    margin-bottom: 12px;
  }

  input {
    flex: 1;
    height: 40px;
    border: 1px solid #eee;
    border-radius: 20px;
    padding: 0 12px;
    outline: none;
    background-color: #f4f7fd;
    width: 100%;
  }
}
</style>
