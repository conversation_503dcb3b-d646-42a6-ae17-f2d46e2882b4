/**
 * 统一验证管理器
 * 支持在 Geetest 和 Cloudflare Turnstile 之间切换
 */

import { GeetestMgr } from "./GeetestMgr";
import { CloudflareMgr, CF_TURNSTILE_TYPE } from "./CloudflareMgr";

// 类型定义

export enum VERIFICATION_TYPE {
  GEETEST = "geetest",
  CLOUDFLARE = "cloudflare",
}

// 验证场景 CF校验  TODO 调整
export const VERIFICATION_TYPES = {
  password_login: "SCENE_LOGIN",
  phone_code_login: "SCENE_LOGIN",
  phone_login_code: "SCENE_GET_CODE",
  forget_password_code: "SCENE_GET_CODE",
  change_pay_password_code: "SCENE_GET_CODE",
  bind_withdraw_account_code: "SCENE_GET_CODE",
  change_withdraw_account_code: "SCENE_GET_CODE",
};

// 验证结果接口
export interface VerificationResult {
  success: boolean;
  type: VERIFICATION_TYPE;
  error?: string;
  errorCode?: string;
  retryable?: boolean;
  data?: {
    geetest_guard?: string;
    userInfo?: string;
    geetest_captcha?: string;
    buds?: string;
    "cf-token"?: string;
    "cf-scene"?: string;
    cf_token?: string;
    cf_type?: string;
  };
}

// 验证配置接口
export interface VerificationConfig {
  type: VERIFICATION_TYPE;
  containerId?: string;
  phone?: string;
}

// 核心管理器类

export class VerificationMgr {
  private static _instance: VerificationMgr;
  private currentType: VERIFICATION_TYPE;

  static get instance() {
    if (this._instance) {
      return this._instance;
    }
    this._instance = new VerificationMgr();
    return this._instance;
  }

  constructor() {
    // 从环境变量或配置中获取默认验证类型
    this.currentType = this.getDefaultVerificationType();
  }

  /**
   * 获取默认验证类型
   */
  private getDefaultVerificationType(): VERIFICATION_TYPE {
    const envType = import.meta.env.VITE_VERIFICATION_TYPE;

    if (envType === "cloudflare") {
      return VERIFICATION_TYPE.CLOUDFLARE;
    } else if (envType === "geetest") {
      return VERIFICATION_TYPE.GEETEST;
    }

    return VERIFICATION_TYPE.GEETEST;
  }

  /**
   * 设置验证类型
   */
  setVerificationType(type: VERIFICATION_TYPE): void {
    this.currentType = type;
  }

  /**
   * 执行验证
   * @param sceneType 验证场景类型
   * @param callback 回调函数
   * @param config 验证配置
   *
   */
  async verify(
    sceneType: string,
    callback: (result: VerificationResult | false) => void,
    config?: Partial<VerificationConfig>
  ): Promise<void> {
    const verificationType = config?.type || this.currentType;

    try {
      switch (verificationType) {
        case VERIFICATION_TYPE.GEETEST:
          await this.executeGeetestVerification(sceneType, callback, config);
          break;

        case VERIFICATION_TYPE.CLOUDFLARE:
          await this.executeCloudflareVerification(VERIFICATION_TYPES[sceneType], callback, config);
          break;

        default:
          callback(false);
      }
    } catch (error) {
      callback(false);
    }
  }

  /**
   * 执行 Geetest 验证
   */
  private async executeGeetestVerification(
    sceneType: string,
    callback: (result: VerificationResult | false) => void,
    config?: Partial<VerificationConfig>
  ): Promise<void> {
    const geetestCallback = (result: any) => {
      if (result === false) {
        callback(false);
        return;
      }

      const verificationResult: VerificationResult = {
        success: true,
        type: VERIFICATION_TYPE.GEETEST,
        data: {
          geetest_guard: result.geetest_guard || "",
          userInfo: result.userInfo || "",
          geetest_captcha: result.geetest_captcha || "",
          buds: result.buds || "64",
        },
      };

      callback(verificationResult);
    };

    // 调用 Geetest 验证
    GeetestMgr.instance.geetest_device(sceneType, geetestCallback, config?.phone);
  }

  /**
   * 执行 Cloudflare 验证
   * 将场景类型字符串映射到 CF_TURNSTILE_TYPE 枚举
   */
  private mapSceneTypeToCFType(sceneType: string): CF_TURNSTILE_TYPE {
    // 创建反向映射，从枚举值到枚举键
    const cfTypeMap: Record<string, CF_TURNSTILE_TYPE> = {};
    Object.values(CF_TURNSTILE_TYPE).forEach((value) => {
      cfTypeMap[value] = value as CF_TURNSTILE_TYPE;
    });

    // 如果直接匹配到枚举值，返回对应的枚举
    if (cfTypeMap[sceneType]) {
      return cfTypeMap[sceneType];
    }

    // 默认场景映射
    const sceneMapping: Record<string, CF_TURNSTILE_TYPE> = {
      login: CF_TURNSTILE_TYPE.LOGIN_SUBMIT,
      register: CF_TURNSTILE_TYPE.LOGIN_PHONE_GET_CODE,
      forgot_password: CF_TURNSTILE_TYPE.FORGET_PW_SUBMIT,
      change_password: CF_TURNSTILE_TYPE.MODIFY_LOGIN_PW_SUBMIT,
      withdraw: CF_TURNSTILE_TYPE.WITHDRAWAL_SUBMIT,
      kyc: CF_TURNSTILE_TYPE.KYC_SUBMIT,
      bind_phone: CF_TURNSTILE_TYPE.BIND_PHONE_SUBMIT,
      change_phone: CF_TURNSTILE_TYPE.MODIFY_PHONE_SUBMIT,
    };

    return sceneMapping[sceneType] || CF_TURNSTILE_TYPE.LOGIN_SUBMIT;
  }

  private async executeCloudflareVerification(
    sceneType: string,
    callback: (result: VerificationResult | false) => void,
    config?: Partial<VerificationConfig>
  ): Promise<void> {
    const cfType = this.mapSceneTypeToCFType(sceneType);

    // 根据 SITE_KEY_MAP 中的 mode 配置决定是否使用无感验证
    const cloudflareManager = CloudflareMgr.instance;
    const useSeamless = cloudflareManager.isInvisibleVerification(cfType);

    try {
      let result;

      if (useSeamless) {
        const { executeSeamlessVerification } = await import("./CloudflareVerifyAPI");
        const verifyOptions: any = {
          cfType,
          theme: "light",
          size: "normal",
        };
        result = await executeSeamlessVerification(verifyOptions);
      } else {
        const { showCloudflareVerify } = await import("./CloudflareVerifyAPI");
        const verifyOptions: any = { cfType };

        if (config?.containerId) {
          verifyOptions.containerId = config.containerId;
        }

        result = await showCloudflareVerify(verifyOptions);
      }

      if (result.success) {
        const verificationResult: VerificationResult = {
          success: true,
          type: VERIFICATION_TYPE.CLOUDFLARE,
          data: {
            "cf-token": result["cf-token"] || result.token || "",
            "cf-scene": result["cf-scene"] || result.cfType || sceneType,
            cf_token: result["cf-token"] || result.token || "",
            cf_type: result["cf-scene"] || result.cfType || sceneType,
          },
        };
        callback(verificationResult);
      } else {
        const verificationResult: VerificationResult = {
          success: false,
          type: VERIFICATION_TYPE.CLOUDFLARE,
          error: result.error || "Verification failed",
          errorCode: result.errorCode,
          retryable: result.retryable,
          data: {
            "cf-scene": result["cf-scene"] || sceneType,
            cf_type: result["cf-scene"] || sceneType,
          },
        };
        callback(verificationResult);
      }
    } catch (error) {
      callback(false);
    }
  }

  /**
   * 检查验证类型是否可用
   */
  async isVerificationTypeAvailable(type: VERIFICATION_TYPE): Promise<boolean> {
    switch (type) {
      case VERIFICATION_TYPE.GEETEST:
        return typeof window !== "undefined" && !!(window as any).initGeeGuard;
      case VERIFICATION_TYPE.CLOUDFLARE:
        try {
          const mgr = CloudflareMgr.instance;
          await mgr["initTurnstileScript"]();
          return true;
        } catch {
          return false;
        }
      default:
        return false;
    }
  }

  /**
   * 获取验证类型的显示名称
   */
  getVerificationTypeName(type?: VERIFICATION_TYPE): string {
    const targetType = type || this.currentType;
    switch (targetType) {
      case VERIFICATION_TYPE.GEETEST:
        return "Geetest";
      case VERIFICATION_TYPE.CLOUDFLARE:
        return "Cloudflare Turnstile";
      default:
        return "Unknown";
    }
  }
}

// 导出

export const verificationMgr = VerificationMgr.instance;

/**
 * 便捷的验证函数
 * @param sceneType 验证场景类型
 * @param callback 回调函数
 * @param config 验证配置
 */
export const executeVerification = async (
  sceneType: string,
  callback: (result: VerificationResult | false) => void,
  config?: Partial<VerificationConfig>
): Promise<void> => {
  return verificationMgr.verify(sceneType, callback, config);
};
